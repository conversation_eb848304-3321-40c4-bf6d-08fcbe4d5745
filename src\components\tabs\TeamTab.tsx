
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import {
  Users,
  UserPlus,
  Settings,
  MessageCircle,
  CreditCard,
  Crown,
  TrendingUp,
  AlertTriangle,
  Zap
} from 'lucide-react';
import { useBilling } from '@/hooks/useBilling';
import { BillingDialog } from '@/components/billing/BillingDialog';

const TEAM_MEMBERS = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: '<PERSON><PERSON><PERSON>',
    permissions: ['setujui_kas_kecil', 'lihat_laporan', 'kelola_tim'],
    avatar: 'BS',
    status: 'aktif'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'Akuntan',
    permissions: ['setujui_kas_kecil', 'lihat_laporan'],
    avatar: 'SW',
    status: 'aktif'
  },
  {
    id: '3',
    name: 'Ahmad Rahman',
    email: '<EMAIL>',
    role: 'Ketua Tim',
    permissions: ['setujui_kas_kecil'],
    avatar: 'AR',
    status: 'pending'
  }
];

export const TeamTab = () => {
  const [newMemberEmail, setNewMemberEmail] = useState('');
  const [showAddMember, setShowAddMember] = useState(false);
  const [whatsappConnected, setWhatsappConnected] = useState(false);
  const [billingDialogOpen, setBillingDialogOpen] = useState(false);

  const {
    loading: billingLoading,
    currentTeam,
    getCurrentPlan,
    getUsageSummary,
    getRemainingTransactions,
    isAtTransactionLimit,
    needsUpgrade,
    canManageBilling
  } = useBilling();

  const handleAddMember = () => {
    console.log('Menambah anggota:', newMemberEmail);
    setNewMemberEmail('');
    setShowAddMember(false);
  };

  const handleConnectWhatsApp = () => {
    console.log('Menghubungkan WhatsApp...');
    setWhatsappConnected(true);
  };

  const currentPlan = getCurrentPlan();
  const usageSummary = getUsageSummary();
  const remainingTransactions = getRemainingTransactions();
  const isUnlimited = currentPlan?.name === 'unlimited';
  const isFree = currentPlan?.name === 'free';

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getUsagePercentage = () => {
    if (!usageSummary || isUnlimited || !usageSummary.daily_limit) return 0;
    return (usageSummary.today_usage / usageSummary.daily_limit) * 100;
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'Manajer Keuangan': return 'bg-purple-100 text-purple-800';
      case 'Akuntan': return 'bg-blue-100 text-blue-800';
      case 'Ketua Tim': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'aktif': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'tidak_aktif': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Manajemen Tim</h1>

        {/* Billing & Usage Overview */}
        {currentTeam && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Current Plan */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Plan Saat Ini
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {isUnlimited ? (
                      <Crown className="w-5 h-5 text-yellow-500" />
                    ) : (
                      <Zap className="w-5 h-5 text-blue-500" />
                    )}
                    <span className="font-semibold">{currentPlan?.display_name || 'Free Plan'}</span>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {isFree ? 'Gratis' : formatPrice(currentPlan?.price_monthly || 0)}
                    {!isFree && <span className="text-xs"> /bulan</span>}
                  </div>

                  {needsUpgrade() && (
                    <div className="bg-red-50 border border-red-200 rounded p-2">
                      <div className="flex items-center gap-1 text-red-700 text-xs">
                        <AlertTriangle className="w-3 h-3" />
                        Limit tercapai
                      </div>
                    </div>
                  )}

                  <Button
                    onClick={() => setBillingDialogOpen(true)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    Kelola Billing
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Usage Today */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Penggunaan Hari Ini
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">{usageSummary?.today_usage || 0}</span>
                    <span className="text-sm text-muted-foreground">
                      {isUnlimited ? 'unlimited' : `/ ${usageSummary?.daily_limit || 10}`}
                    </span>
                  </div>

                  {!isUnlimited && usageSummary?.daily_limit && (
                    <div className="space-y-2">
                      <Progress
                        value={getUsagePercentage()}
                        className="h-2"
                      />
                      <div className="text-xs text-muted-foreground">
                        {remainingTransactions} transaksi tersisa
                      </div>
                    </div>
                  )}

                  {isAtTransactionLimit() && (
                    <Badge className="bg-red-100 text-red-800 text-xs">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Limit Tercapai
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Team Members Count */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Anggota Tim
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">{usageSummary?.total_members || TEAM_MEMBERS.length}</span>
                    <span className="text-sm text-muted-foreground">
                      {currentPlan?.features?.team_members === null
                        ? 'unlimited'
                        : `/ ${currentPlan?.features?.team_members || 'N/A'}`
                      }
                    </span>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    {currentPlan?.features?.team_members === null
                      ? 'Tambah anggota tanpa batas'
                      : `${Math.max(0, (currentPlan?.features?.team_members || 0) - (usageSummary?.total_members || TEAM_MEMBERS.length))} slot tersisa`
                    }
                  </div>

                  <Button
                    onClick={() => setShowAddMember(true)}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    <UserPlus className="w-3 h-3 mr-1" />
                    Tambah Anggota
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* WhatsApp Integration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Integrasi WhatsApp
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Hubungkan WhatsApp Business</h3>
                <p className="text-sm text-muted-foreground">
                  Aktifkan anggota tim untuk menerima notifikasi dan persetujuan via WhatsApp
                </p>
              </div>
              <div className="flex items-center gap-2">
                {whatsappConnected ? (
                  <Badge className="bg-green-100 text-green-800">Terhubung</Badge>
                ) : (
                  <Button onClick={handleConnectWhatsApp}>
                    Hubungkan WhatsApp
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Team Member */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Tambah Anggota Tim
              </span>
              <Button 
                variant="outline" 
                onClick={() => setShowAddMember(!showAddMember)}
              >
                {showAddMember ? 'Batal' : 'Tambah Anggota'}
              </Button>
            </CardTitle>
          </CardHeader>
          {showAddMember && (
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Alamat Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newMemberEmail}
                    onChange={(e) => setNewMemberEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Izin Default</Label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">Lihat Laporan</Badge>
                    <Badge variant="outline">Setujui Kas Kecil</Badge>
                  </div>
                </div>
                <Button onClick={handleAddMember} disabled={!newMemberEmail}>
                  Kirim Undangan
                </Button>
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Team Members List */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Anggota Tim ({TEAM_MEMBERS.length})</h2>
        
        {TEAM_MEMBERS.map((member) => (
          <Card key={member.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {member.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{member.name}</h3>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={getRoleColor(member.role)}>
                        {member.role}
                      </Badge>
                      <Badge className={getStatusColor(member.status)}>
                        {member.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <Button variant="ghost" size="icon">
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              {/* Permissions */}
              <div className="mt-3 pt-3 border-t">
                <div className="text-sm text-muted-foreground mb-2">Izin:</div>
                <div className="flex flex-wrap gap-1">
                  {member.permissions.map((permission) => (
                    <Badge key={permission} variant="secondary" className="text-xs">
                      {permission.replace('_', ' ')}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Billing Dialog */}
      <BillingDialog
        open={billingDialogOpen}
        onOpenChange={setBillingDialogOpen}
      />
    </div>
  );
};
