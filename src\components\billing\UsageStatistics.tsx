import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Users, 
  Calendar, 
  AlertTriangle,
  Crown,
  Zap
} from 'lucide-react';
import { UsageSummary, BillingPlanWithFeatures } from '@/types/billing';

interface UsageStatisticsProps {
  usageSummary: UsageSummary | null;
  currentPlan: BillingPlanWithFeatures | null;
  onUpgrade?: () => void;
  loading?: boolean;
}

export const UsageStatistics: React.FC<UsageStatisticsProps> = ({
  usageSummary,
  currentPlan,
  onUpgrade,
  loading = false
}) => {
  if (!usageSummary || !currentPlan) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Memuat statistik penggunaan...
          </div>
        </CardContent>
      </Card>
    );
  }

  const isUnlimited = currentPlan.name === 'unlimited';
  const isFree = currentPlan.name === 'free';
  const isAtLimit = usageSummary.remaining_today === 0;
  const isNearLimit = usageSummary.remaining_today !== null && 
                      usageSummary.remaining_today <= 2 && 
                      usageSummary.remaining_today > 0;

  const getUsagePercentage = () => {
    if (isUnlimited || !usageSummary.daily_limit) return 0;
    return (usageSummary.today_usage / usageSummary.daily_limit) * 100;
  };

  const getUsageColor = () => {
    const percentage = getUsagePercentage();
    if (percentage >= 100) return 'bg-red-500';
    if (percentage >= 80) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getStatusBadge = () => {
    if (isUnlimited) {
      return (
        <Badge className="bg-yellow-100 text-yellow-800">
          <Crown className="w-3 h-3 mr-1" />
          Unlimited
        </Badge>
      );
    }

    if (isAtLimit) {
      return (
        <Badge className="bg-red-100 text-red-800">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Limit Tercapai
        </Badge>
      );
    }

    if (isNearLimit) {
      return (
        <Badge className="bg-yellow-100 text-yellow-800">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Mendekati Limit
        </Badge>
      );
    }

    return (
      <Badge className="bg-green-100 text-green-800">
        <Zap className="w-3 h-3 mr-1" />
        Normal
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Current Plan Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Plan Saat Ini</CardTitle>
            {getStatusBadge()}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-lg">{currentPlan.display_name}</h3>
              <p className="text-sm text-muted-foreground">
                {currentPlan.description}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">
                {isFree ? 'Gratis' : 
                 new Intl.NumberFormat('id-ID', {
                   style: 'currency',
                   currency: 'IDR',
                   minimumFractionDigits: 0
                 }).format(currentPlan.price_monthly)}
              </div>
              {!isFree && (
                <div className="text-sm text-muted-foreground">per bulan</div>
              )}
            </div>
          </div>

          {(isAtLimit || isNearLimit) && isFree && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-yellow-800">
                    {isAtLimit ? 'Limit Harian Tercapai' : 'Mendekati Limit Harian'}
                  </h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    {isAtLimit 
                      ? 'Anda telah mencapai batas 10 transaksi hari ini. Upgrade ke plan Unlimited untuk transaksi tanpa batas.'
                      : `Anda hanya memiliki ${usageSummary.remaining_today} transaksi tersisa hari ini.`
                    }
                  </p>
                  <Button
                    onClick={onUpgrade}
                    disabled={loading}
                    size="sm"
                    className="mt-3 bg-yellow-600 hover:bg-yellow-700"
                  >
                    Upgrade ke Unlimited
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Daily Usage */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Penggunaan Hari Ini
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold">{usageSummary.today_usage}</span>
                <span className="text-sm text-muted-foreground">
                  {isUnlimited ? 'unlimited' : `/ ${usageSummary.daily_limit}`}
                </span>
              </div>
              
              {!isUnlimited && usageSummary.daily_limit && (
                <div className="space-y-1">
                  <Progress 
                    value={getUsagePercentage()} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {usageSummary.remaining_today} transaksi tersisa
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="w-4 h-4" />
              Anggota Tim
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold">{usageSummary.total_members}</span>
                <span className="text-sm text-muted-foreground">
                  {currentPlan.features.team_members === null 
                    ? 'unlimited' 
                    : `/ ${currentPlan.features.team_members || 'N/A'}`
                  }
                </span>
              </div>
              
              {currentPlan.features.team_members && (
                <div className="space-y-1">
                  <Progress 
                    value={(usageSummary.total_members / currentPlan.features.team_members) * 100} 
                    className="h-2"
                  />
                  <div className="text-xs text-muted-foreground">
                    {Math.max(0, currentPlan.features.team_members - usageSummary.total_members)} slot tersisa
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Plan Type */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Jenis Plan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {isUnlimited ? (
                  <Crown className="w-5 h-5 text-yellow-500" />
                ) : (
                  <Zap className="w-5 h-5 text-blue-500" />
                )}
                <span className="text-lg font-semibold">
                  {currentPlan.display_name}
                </span>
              </div>
              
              <div className="text-sm text-muted-foreground">
                {isUnlimited 
                  ? 'Akses penuh ke semua fitur'
                  : 'Fitur terbatas dengan kuota harian'
                }
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
