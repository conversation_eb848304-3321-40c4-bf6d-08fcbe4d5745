import { supabase } from '@/integrations/supabase/client';
import {
  Team,
  TeamInsert,
  TeamUpdate,
  TeamMember,
  TeamMemberInsert,
  BillingPlan,
  BillingPlanWithFeatures,
  TransactionUsage,
  UsageSummary,
  CreateTeamRequest,
  TeamInvitationRequest,
  BillingUpgradeRequest,
  UsageTrackingRequest,
  TeamWithMembers,
  ApiResponse,
  GetTeamResponse,
  GetTeamsResponse,
  GetBillingPlansResponse,
  GetUsageSummaryResponse,
  UpgradePlanResponse,
  BILLING_PLANS,
  TEAM_ROLES,
  MEMBER_STATUS
} from '@/types/billing';

class BillingService {
  // Team Management
  async createTeam(request: CreateTeamRequest): Promise<ApiResponse<Team>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { error: 'User not authenticated' };
      }

      const teamData: TeamInsert = {
        name: request.name,
        owner_id: user.id,
        billing_plan: request.billing_plan || BILLING_PLANS.FREE,
        billing_status: 'active'
      };

      const { data: team, error } = await supabase
        .from('teams')
        .insert(teamData)
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      // Add owner as team member
      await this.addTeamMember({
        team_id: team.id,
        email: user.email!,
        role: TEAM_ROLES.OWNER,
        permissions: ['kelola_tim', 'kelola_billing', 'lihat_laporan', 'setujui_kas_kecil']
      });

      // Add initial members if provided
      if (request.initial_members) {
        for (const member of request.initial_members) {
          await this.inviteTeamMember({
            team_id: team.id,
            email: member.email,
            role: member.role,
            permissions: member.permissions
          });
        }
      }

      return { data: team };
    } catch (error) {
      return { error: `Failed to create team: ${error}` };
    }
  }

  async getTeam(teamId: string): Promise<GetTeamResponse> {
    try {
      const { data: team, error } = await supabase
        .from('teams')
        .select(`
          *,
          team_members (
            *,
            user:user_id (
              id,
              email,
              user_metadata
            )
          )
        `)
        .eq('id', teamId)
        .single();

      if (error) {
        return { error: error.message };
      }

      // Get billing plan details
      const { data: billingPlan } = await supabase
        .from('billing_plans')
        .select('*')
        .eq('name', team.billing_plan)
        .single();

      // Get usage summary
      const usageSummary = await this.getUsageSummary(teamId);

      const teamWithMembers: TeamWithMembers = {
        ...team,
        billing_plan_details: billingPlan || undefined,
        usage_summary: usageSummary.data || undefined
      };

      return { data: teamWithMembers };
    } catch (error) {
      return { error: `Failed to get team: ${error}` };
    }
  }

  async getUserTeams(): Promise<GetTeamsResponse> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { error: 'User not authenticated' };
      }

      const { data: teams, error } = await supabase
        .from('teams')
        .select(`
          *,
          team_members (
            *,
            user:user_id (
              id,
              email,
              user_metadata
            )
          )
        `)
        .or(`owner_id.eq.${user.id},team_members.user_id.eq.${user.id}`)
        .eq('team_members.status', MEMBER_STATUS.ACTIVE);

      if (error) {
        return { error: error.message };
      }

      return { data: teams || [] };
    } catch (error) {
      return { error: `Failed to get teams: ${error}` };
    }
  }

  async updateTeam(teamId: string, updates: TeamUpdate): Promise<ApiResponse<Team>> {
    try {
      const { data: team, error } = await supabase
        .from('teams')
        .update(updates)
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      return { data: team };
    } catch (error) {
      return { error: `Failed to update team: ${error}` };
    }
  }

  // Team Member Management
  async inviteTeamMember(request: TeamInvitationRequest): Promise<ApiResponse<TeamMember>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { error: 'User not authenticated' };
      }

      // Check if user exists
      const { data: existingUser } = await supabase.auth.admin.getUserByEmail(request.email);
      
      const memberData: TeamMemberInsert = {
        team_id: request.team_id,
        user_id: existingUser?.user?.id || '', // Will be updated when user signs up
        role: request.role,
        permissions: request.permissions,
        status: existingUser?.user ? MEMBER_STATUS.PENDING : MEMBER_STATUS.PENDING,
        invited_by: user.id
      };

      const { data: member, error } = await supabase
        .from('team_members')
        .insert(memberData)
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      // TODO: Send invitation email
      
      return { data: member };
    } catch (error) {
      return { error: `Failed to invite team member: ${error}` };
    }
  }

  async addTeamMember(request: TeamInvitationRequest): Promise<ApiResponse<TeamMember>> {
    try {
      // Get user by email
      const { data: existingUser } = await supabase.auth.admin.getUserByEmail(request.email);
      
      if (!existingUser?.user) {
        return { error: 'User not found' };
      }

      const memberData: TeamMemberInsert = {
        team_id: request.team_id,
        user_id: existingUser.user.id,
        role: request.role,
        permissions: request.permissions,
        status: MEMBER_STATUS.ACTIVE,
        joined_at: new Date().toISOString()
      };

      const { data: member, error } = await supabase
        .from('team_members')
        .insert(memberData)
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      return { data: member };
    } catch (error) {
      return { error: `Failed to add team member: ${error}` };
    }
  }

  async removeTeamMember(teamId: string, userId: string): Promise<ApiResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('team_id', teamId)
        .eq('user_id', userId);

      if (error) {
        return { error: error.message };
      }

      return { data: true };
    } catch (error) {
      return { error: `Failed to remove team member: ${error}` };
    }
  }

  // Billing Plans
  async getBillingPlans(): Promise<GetBillingPlansResponse> {
    try {
      const { data: plans, error } = await supabase
        .from('billing_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        return { error: error.message };
      }

      const plansWithFeatures: BillingPlanWithFeatures[] = plans.map(plan => ({
        ...plan,
        features: typeof plan.features === 'object' ? plan.features as any : {}
      }));

      return { data: plansWithFeatures };
    } catch (error) {
      return { error: `Failed to get billing plans: ${error}` };
    }
  }

  // Usage Tracking
  async trackTransaction(request: UsageTrackingRequest): Promise<ApiResponse<number>> {
    try {
      // Check if team can perform transaction
      const { data: canPerform, error: checkError } = await supabase
        .rpc('can_perform_transaction', {
          p_team_id: request.team_id,
          p_user_id: request.user_id
        });

      if (checkError) {
        return { error: checkError.message };
      }

      if (!canPerform) {
        return { error: 'Daily transaction limit exceeded. Please upgrade your plan.' };
      }

      // Increment usage
      const { data: newCount, error } = await supabase
        .rpc('increment_transaction_usage', {
          p_team_id: request.team_id,
          p_user_id: request.user_id,
          p_transaction_type: request.transaction_type
        });

      if (error) {
        return { error: error.message };
      }

      return { data: newCount };
    } catch (error) {
      return { error: `Failed to track transaction: ${error}` };
    }
  }

  async getUsageSummary(teamId: string): Promise<GetUsageSummaryResponse> {
    try {
      const { data: summary, error } = await supabase
        .rpc('get_team_usage_summary', { p_team_id: teamId });

      if (error) {
        return { error: error.message };
      }

      return { data: summary?.[0] || null };
    } catch (error) {
      return { error: `Failed to get usage summary: ${error}` };
    }
  }

  async canPerformTransaction(teamId: string, userId: string): Promise<ApiResponse<boolean>> {
    try {
      const { data: canPerform, error } = await supabase
        .rpc('can_perform_transaction', {
          p_team_id: teamId,
          p_user_id: userId
        });

      if (error) {
        return { error: error.message };
      }

      return { data: canPerform };
    } catch (error) {
      return { error: `Failed to check transaction permission: ${error}` };
    }
  }

  // Billing Upgrade
  async upgradePlan(request: BillingUpgradeRequest): Promise<UpgradePlanResponse> {
    try {
      // Update team billing plan
      const { error: updateError } = await supabase
        .from('teams')
        .update({
          billing_plan: request.new_plan,
          subscription_start_date: new Date().toISOString(),
          subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        })
        .eq('id', request.team_id);

      if (updateError) {
        return { error: updateError.message };
      }

      // TODO: Integrate with payment gateway
      // For now, we'll just return success
      return { 
        data: { 
          success: true,
          payment_url: '/billing/success' // Mock payment URL
        }
      };
    } catch (error) {
      return { error: `Failed to upgrade plan: ${error}` };
    }
  }
}

export const billingService = new BillingService();
