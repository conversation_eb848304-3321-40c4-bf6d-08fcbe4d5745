import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { billingService } from '@/services/billingService';
import {
  Team,
  TeamWithMembers,
  BillingPlanWithFeatures,
  UsageSummary,
  CreateTeamRequest,
  TeamInvitationRequest,
  BillingUpgradeRequest,
  UsageTrackingRequest
} from '@/types/billing';
import { toast } from 'sonner';

export const useBilling = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [teams, setTeams] = useState<TeamWithMembers[]>([]);
  const [currentTeam, setCurrentTeam] = useState<TeamWithMembers | null>(null);
  const [billingPlans, setBillingPlans] = useState<BillingPlanWithFeatures[]>([]);

  // Load user teams on mount
  useEffect(() => {
    if (user) {
      loadUserTeams();
      loadBillingPlans();
    }
  }, [user]);

  const loadUserTeams = async () => {
    setLoading(true);
    try {
      const response = await billingService.getUserTeams();
      if (response.error) {
        toast.error(response.error);
        return;
      }
      setTeams(response.data || []);
      
      // Set first team as current if none selected
      if (!currentTeam && response.data && response.data.length > 0) {
        setCurrentTeam(response.data[0]);
      }
    } catch (error) {
      toast.error('Failed to load teams');
      console.error('Error loading teams:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBillingPlans = async () => {
    try {
      const response = await billingService.getBillingPlans();
      if (response.error) {
        toast.error(response.error);
        return;
      }
      setBillingPlans(response.data || []);
    } catch (error) {
      toast.error('Failed to load billing plans');
      console.error('Error loading billing plans:', error);
    }
  };

  const createTeam = async (request: CreateTeamRequest) => {
    setLoading(true);
    try {
      const response = await billingService.createTeam(request);
      if (response.error) {
        toast.error(response.error);
        return false;
      }
      
      toast.success('Team created successfully');
      await loadUserTeams();
      return true;
    } catch (error) {
      toast.error('Failed to create team');
      console.error('Error creating team:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const inviteTeamMember = async (request: TeamInvitationRequest) => {
    setLoading(true);
    try {
      const response = await billingService.inviteTeamMember(request);
      if (response.error) {
        toast.error(response.error);
        return false;
      }
      
      toast.success('Team member invited successfully');
      await refreshCurrentTeam();
      return true;
    } catch (error) {
      toast.error('Failed to invite team member');
      console.error('Error inviting team member:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeTeamMember = async (teamId: string, userId: string) => {
    setLoading(true);
    try {
      const response = await billingService.removeTeamMember(teamId, userId);
      if (response.error) {
        toast.error(response.error);
        return false;
      }
      
      toast.success('Team member removed successfully');
      await refreshCurrentTeam();
      return true;
    } catch (error) {
      toast.error('Failed to remove team member');
      console.error('Error removing team member:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const upgradePlan = async (request: BillingUpgradeRequest) => {
    setLoading(true);
    try {
      const response = await billingService.upgradePlan(request);
      if (response.error) {
        toast.error(response.error);
        return false;
      }
      
      toast.success('Plan upgraded successfully');
      await refreshCurrentTeam();
      
      // Redirect to payment if needed
      if (response.data?.payment_url) {
        window.location.href = response.data.payment_url;
      }
      
      return true;
    } catch (error) {
      toast.error('Failed to upgrade plan');
      console.error('Error upgrading plan:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const trackTransaction = async (request: UsageTrackingRequest) => {
    try {
      const response = await billingService.trackTransaction(request);
      if (response.error) {
        toast.error(response.error);
        return false;
      }
      
      // Refresh usage summary if tracking current team
      if (currentTeam && request.team_id === currentTeam.id) {
        await refreshCurrentTeam();
      }
      
      return true;
    } catch (error) {
      toast.error('Failed to track transaction');
      console.error('Error tracking transaction:', error);
      return false;
    }
  };

  const canPerformTransaction = async (teamId: string, userId: string): Promise<boolean> => {
    try {
      const response = await billingService.canPerformTransaction(teamId, userId);
      if (response.error) {
        console.error('Error checking transaction permission:', response.error);
        return false;
      }
      return response.data || false;
    } catch (error) {
      console.error('Error checking transaction permission:', error);
      return false;
    }
  };

  const refreshCurrentTeam = async () => {
    if (!currentTeam) return;
    
    try {
      const response = await billingService.getTeam(currentTeam.id);
      if (response.error) {
        toast.error(response.error);
        return;
      }
      
      setCurrentTeam(response.data || null);
      
      // Update in teams list as well
      setTeams(prev => prev.map(team => 
        team.id === currentTeam.id ? (response.data || team) : team
      ));
    } catch (error) {
      console.error('Error refreshing current team:', error);
    }
  };

  const switchTeam = async (teamId: string) => {
    const team = teams.find(t => t.id === teamId);
    if (team) {
      setCurrentTeam(team);
      // Refresh team data
      const response = await billingService.getTeam(teamId);
      if (response.data) {
        setCurrentTeam(response.data);
      }
    }
  };

  // Helper functions
  const getCurrentPlan = (): BillingPlanWithFeatures | null => {
    if (!currentTeam) return null;
    return billingPlans.find(plan => plan.name === currentTeam.billing_plan) || null;
  };

  const getUsageSummary = (): UsageSummary | null => {
    return currentTeam?.usage_summary || null;
  };

  const isOwner = (): boolean => {
    if (!currentTeam || !user) return false;
    return currentTeam.owner_id === user.id;
  };

  const isAdmin = (): boolean => {
    if (!currentTeam || !user) return false;
    const member = currentTeam.team_members?.find(m => m.user_id === user.id);
    return member?.role === 'admin' || member?.role === 'owner';
  };

  const canManageBilling = (): boolean => {
    if (!currentTeam || !user) return false;
    const member = currentTeam.team_members?.find(m => m.user_id === user.id);
    return member?.permissions?.includes('kelola_billing') || isOwner();
  };

  const canManageTeam = (): boolean => {
    if (!currentTeam || !user) return false;
    const member = currentTeam.team_members?.find(m => m.user_id === user.id);
    return member?.permissions?.includes('kelola_tim') || isOwner();
  };

  const getRemainingTransactions = (): number | null => {
    const usage = getUsageSummary();
    return usage?.remaining_today || null;
  };

  const isAtTransactionLimit = (): boolean => {
    const remaining = getRemainingTransactions();
    return remaining !== null && remaining <= 0;
  };

  const needsUpgrade = (): boolean => {
    const plan = getCurrentPlan();
    return plan?.name === 'free' && isAtTransactionLimit();
  };

  return {
    // State
    loading,
    teams,
    currentTeam,
    billingPlans,
    
    // Actions
    createTeam,
    inviteTeamMember,
    removeTeamMember,
    upgradePlan,
    trackTransaction,
    canPerformTransaction,
    refreshCurrentTeam,
    switchTeam,
    loadUserTeams,
    
    // Helpers
    getCurrentPlan,
    getUsageSummary,
    isOwner,
    isAdmin,
    canManageBilling,
    canManageTeam,
    getRemainingTransactions,
    isAtTransactionLimit,
    needsUpgrade
  };
};
