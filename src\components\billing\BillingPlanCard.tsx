import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Zap } from 'lucide-react';
import { BillingPlanWithFeatures } from '@/types/billing';

interface BillingPlanCardProps {
  plan: BillingPlanWithFeatures;
  currentPlan?: string;
  onUpgrade?: (planName: string) => void;
  loading?: boolean;
}

export const BillingPlanCard: React.FC<BillingPlanCardProps> = ({
  plan,
  currentPlan,
  onUpgrade,
  loading = false
}) => {
  const isCurrentPlan = currentPlan === plan.name;
  const isFree = plan.name === 'free';
  const isUnlimited = plan.name === 'unlimited';

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getFeatureList = () => {
    const features = [];
    
    if (plan.daily_transaction_limit) {
      features.push(`${plan.daily_transaction_limit} transaksi per hari`);
    } else {
      features.push('Transaksi unlimited');
    }

    if (plan.features.team_members) {
      features.push(`Maksimal ${plan.features.team_members} anggota tim`);
    } else if (plan.features.team_members === null) {
      features.push('Anggota tim unlimited');
    }

    if (plan.features.whatsapp_integration) {
      features.push('Integrasi WhatsApp');
    }

    if (plan.features.basic_reports) {
      features.push('Laporan dasar');
    }

    if (plan.features.advanced_reports) {
      features.push('Laporan lanjutan');
    }

    if (plan.features.priority_support) {
      features.push('Dukungan prioritas');
    }

    if (plan.features.custom_integrations) {
      features.push('Integrasi kustom');
    }

    if (plan.features.api_access) {
      features.push('Akses API');
    }

    if (plan.features.data_export) {
      features.push('Export data');
    }

    return features;
  };

  const getPlanIcon = () => {
    if (isUnlimited) return <Crown className="w-5 h-5 text-yellow-500" />;
    if (isFree) return <Zap className="w-5 h-5 text-blue-500" />;
    return null;
  };

  const getCardClassName = () => {
    let baseClass = "relative transition-all duration-200 hover:shadow-lg";
    
    if (isCurrentPlan) {
      baseClass += " ring-2 ring-primary border-primary";
    }
    
    if (isUnlimited) {
      baseClass += " border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50";
    }
    
    return baseClass;
  };

  const features = getFeatureList();

  return (
    <Card className={getCardClassName()}>
      {isUnlimited && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-yellow-500 text-white px-3 py-1">
            Paling Populer
          </Badge>
        </div>
      )}
      
      <CardHeader className="text-center pb-4">
        <div className="flex items-center justify-center gap-2 mb-2">
          {getPlanIcon()}
          <CardTitle className="text-xl">{plan.display_name}</CardTitle>
          {isCurrentPlan && (
            <Badge variant="secondary" className="ml-2">
              Aktif
            </Badge>
          )}
        </div>
        
        <div className="space-y-1">
          <div className="text-3xl font-bold">
            {isFree ? 'Gratis' : formatPrice(plan.price_monthly)}
          </div>
          {!isFree && (
            <div className="text-sm text-muted-foreground">per bulan</div>
          )}
        </div>
        
        {plan.description && (
          <p className="text-sm text-muted-foreground mt-2">
            {plan.description}
          </p>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-3">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-2">
              <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>

        <div className="pt-4">
          {isCurrentPlan ? (
            <Button disabled className="w-full">
              Plan Saat Ini
            </Button>
          ) : (
            <Button
              onClick={() => onUpgrade?.(plan.name)}
              disabled={loading}
              className={`w-full ${
                isUnlimited 
                  ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
                  : ''
              }`}
              variant={isUnlimited ? 'default' : 'outline'}
            >
              {loading ? 'Memproses...' : 
               isFree ? 'Downgrade ke Free' : 'Upgrade Sekarang'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
