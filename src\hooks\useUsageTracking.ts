import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBilling } from '@/hooks/useBilling';
import { toast } from 'sonner';

export const useUsageTracking = () => {
  const { user } = useAuth();
  const { 
    currentTeam, 
    trackTransaction, 
    canPerformTransaction,
    isAtTransactionLimit,
    needsUpgrade,
    getRemainingTransactions
  } = useBilling();

  // Track a transaction and check limits
  const trackAndValidateTransaction = useCallback(async (
    transactionType: string = 'general'
  ): Promise<boolean> => {
    if (!user || !currentTeam) {
      toast.error('User atau tim tidak ditemukan');
      return false;
    }

    try {
      // Check if user can perform transaction
      const canPerform = await canPerformTransaction(currentTeam.id, user.id);
      
      if (!canPerform) {
        toast.error('Batas transaksi harian tercapai. Upgrade ke plan Unlimited untuk transaksi tanpa batas.');
        return false;
      }

      // Track the transaction
      const success = await trackTransaction({
        team_id: currentTeam.id,
        user_id: user.id,
        transaction_type: transactionType
      });

      if (!success) {
        return false;
      }

      // Show warning if approaching limit
      const remaining = getRemainingTransactions();
      if (remaining !== null && remaining <= 2 && remaining > 0) {
        toast.warning(`Perhatian: Anda hanya memiliki ${remaining} transaksi tersisa hari ini.`);
      }

      return true;
    } catch (error) {
      console.error('Error tracking transaction:', error);
      toast.error('Gagal melacak transaksi');
      return false;
    }
  }, [user, currentTeam, canPerformTransaction, trackTransaction, getRemainingTransactions]);

  // Check if user can perform a transaction without tracking it
  const validateTransactionLimit = useCallback(async (): Promise<boolean> => {
    if (!user || !currentTeam) {
      return false;
    }

    try {
      const canPerform = await canPerformTransaction(currentTeam.id, user.id);
      
      if (!canPerform) {
        toast.error('Batas transaksi harian tercapai. Upgrade ke plan Unlimited untuk transaksi tanpa batas.');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating transaction limit:', error);
      return false;
    }
  }, [user, currentTeam, canPerformTransaction]);

  // Show upgrade prompt if needed
  const showUpgradePromptIfNeeded = useCallback(() => {
    if (needsUpgrade()) {
      toast.error(
        'Batas transaksi harian tercapai. Upgrade ke plan Unlimited untuk transaksi tanpa batas.',
        {
          action: {
            label: 'Upgrade Sekarang',
            onClick: () => {
              // This would open the billing dialog
              // You can implement this based on your app structure
              console.log('Open billing dialog for upgrade');
            }
          },
          duration: 10000
        }
      );
    }
  }, [needsUpgrade]);

  // Get usage status for UI display
  const getUsageStatus = useCallback(() => {
    const remaining = getRemainingTransactions();
    const atLimit = isAtTransactionLimit();
    
    return {
      remaining,
      atLimit,
      needsUpgrade: needsUpgrade(),
      canPerformMore: remaining === null || remaining > 0
    };
  }, [getRemainingTransactions, isAtTransactionLimit, needsUpgrade]);

  return {
    // Main functions
    trackAndValidateTransaction,
    validateTransactionLimit,
    showUpgradePromptIfNeeded,
    
    // Status helpers
    getUsageStatus,
    isAtTransactionLimit,
    needsUpgrade,
    getRemainingTransactions,
    
    // Current state
    currentTeam,
    user
  };
};
