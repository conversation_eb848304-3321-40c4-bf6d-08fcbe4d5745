import { Database } from '@/integrations/supabase/types';

// Extract types from Database
export type Team = Database['public']['Tables']['teams']['Row'];
export type TeamInsert = Database['public']['Tables']['teams']['Insert'];
export type TeamUpdate = Database['public']['Tables']['teams']['Update'];

export type TeamMember = Database['public']['Tables']['team_members']['Row'];
export type TeamMemberInsert = Database['public']['Tables']['team_members']['Insert'];
export type TeamMemberUpdate = Database['public']['Tables']['team_members']['Update'];

export type BillingPlan = Database['public']['Tables']['billing_plans']['Row'];
export type BillingPlanInsert = Database['public']['Tables']['billing_plans']['Insert'];
export type BillingPlanUpdate = Database['public']['Tables']['billing_plans']['Update'];

export type TransactionUsage = Database['public']['Tables']['transaction_usage']['Row'];
export type TransactionUsageInsert = Database['public']['Tables']['transaction_usage']['Insert'];
export type TransactionUsageUpdate = Database['public']['Tables']['transaction_usage']['Update'];

export type BillingHistory = Database['public']['Tables']['billing_history']['Row'];
export type BillingHistoryInsert = Database['public']['Tables']['billing_history']['Insert'];
export type BillingHistoryUpdate = Database['public']['Tables']['billing_history']['Update'];

// Extended types with relationships
export interface TeamWithMembers extends Team {
  team_members: TeamMemberWithUser[];
  billing_plan_details?: BillingPlan;
  usage_summary?: UsageSummary;
}

export interface TeamMemberWithUser extends TeamMember {
  user?: {
    id: string;
    email?: string;
    user_metadata?: {
      full_name?: string;
      avatar_url?: string;
    };
  };
}

export interface UsageSummary {
  total_members: number;
  current_plan: string;
  daily_limit: number | null;
  today_usage: number;
  remaining_today: number | null;
}

// Billing plan features interface
export interface BillingPlanFeatures {
  whatsapp_integration?: boolean;
  basic_reports?: boolean;
  advanced_reports?: boolean;
  team_members?: number | null; // null means unlimited
  priority_support?: boolean;
  custom_integrations?: boolean;
  api_access?: boolean;
  data_export?: boolean;
}

// Billing plan with parsed features
export interface BillingPlanWithFeatures extends Omit<BillingPlan, 'features'> {
  features: BillingPlanFeatures;
}

// Team role permissions
export type TeamRole = 'owner' | 'admin' | 'member';
export type TeamMemberStatus = 'pending' | 'active' | 'suspended';
export type BillingStatus = 'active' | 'suspended' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// Permission types
export type TeamPermission = 
  | 'setujui_kas_kecil'
  | 'lihat_laporan'
  | 'kelola_tim'
  | 'kelola_billing'
  | 'kelola_integrasi'
  | 'export_data';

// Billing upgrade request
export interface BillingUpgradeRequest {
  team_id: string;
  new_plan: string;
  payment_method?: string;
  billing_period_months?: number;
}

// Usage tracking request
export interface UsageTrackingRequest {
  team_id: string;
  user_id: string;
  transaction_type?: string;
}

// Team creation request
export interface CreateTeamRequest {
  name: string;
  billing_plan?: string;
  initial_members?: {
    email: string;
    role: TeamRole;
    permissions: TeamPermission[];
  }[];
}

// Team invitation request
export interface TeamInvitationRequest {
  team_id: string;
  email: string;
  role: TeamRole;
  permissions: TeamPermission[];
}

// Billing analytics
export interface BillingAnalytics {
  total_revenue: number;
  active_subscriptions: number;
  free_teams: number;
  paid_teams: number;
  monthly_growth: number;
  churn_rate: number;
}

// Usage analytics
export interface UsageAnalytics {
  total_transactions: number;
  daily_average: number;
  peak_usage_day: string;
  most_active_team: string;
  usage_by_plan: {
    plan_name: string;
    total_usage: number;
    team_count: number;
  }[];
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

// Billing service responses
export type CreateTeamResponse = ApiResponse<Team>;
export type GetTeamResponse = ApiResponse<TeamWithMembers>;
export type GetTeamsResponse = PaginatedResponse<TeamWithMembers>;
export type GetBillingPlansResponse = ApiResponse<BillingPlanWithFeatures[]>;
export type GetUsageSummaryResponse = ApiResponse<UsageSummary>;
export type UpgradePlanResponse = ApiResponse<{ success: boolean; payment_url?: string }>;

// Webhook payload types
export interface PaymentWebhookPayload {
  event_type: 'payment.success' | 'payment.failed' | 'subscription.cancelled';
  team_id: string;
  payment_reference: string;
  amount: number;
  currency: string;
  timestamp: string;
}

// Constants
export const BILLING_PLANS = {
  FREE: 'free',
  UNLIMITED: 'unlimited'
} as const;

export const TEAM_ROLES = {
  OWNER: 'owner',
  ADMIN: 'admin',
  MEMBER: 'member'
} as const;

export const TEAM_PERMISSIONS = {
  APPROVE_PETTY_CASH: 'setujui_kas_kecil',
  VIEW_REPORTS: 'lihat_laporan',
  MANAGE_TEAM: 'kelola_tim',
  MANAGE_BILLING: 'kelola_billing',
  MANAGE_INTEGRATIONS: 'kelola_integrasi',
  EXPORT_DATA: 'export_data'
} as const;

export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded'
} as const;

export const BILLING_STATUS = {
  ACTIVE: 'active',
  SUSPENDED: 'suspended',
  CANCELLED: 'cancelled'
} as const;

export const MEMBER_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active',
  SUSPENDED: 'suspended'
} as const;
