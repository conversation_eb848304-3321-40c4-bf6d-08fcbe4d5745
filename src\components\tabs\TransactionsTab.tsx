import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Filter, RefreshCw, AlertTriangle, TrendingUp } from 'lucide-react';
import { InvoiceDetailDialog } from '@/components/InvoiceDetailDialog';
import { VendorBillDetailDialog } from '@/components/VendorBillDetailDialog';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useUsageTracking } from '@/hooks/useUsageTracking';
import { bigCapitalService } from '@/services/bigCapitalService';

// Updated interface to match the new API response format
interface Invoice {
  id: number;
  invoice_no: string;
  customer_id: number;
  customer: {
    id: number;
    display_name: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  invoice_date: string;
  invoice_date_formatted: string;
  due_date: string;
  due_date_formatted: string;
  total: number;
  total_formatted: string;
  balance: number;
  due_amount: number;
  due_amount_formatted: string;
  payment_amount: number;
  payment_amount_formatted: string;
  currency_code: string;
  created_at: string;
  created_at_formatted: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  invoice_message: string;
  terms_conditions: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string;
    };
  }>;
}

// Interface for vendor bills from BigCapital API
interface VendorBill {
  id: number;
  bill_number: string | null;
  vendor_id: number;
  vendor: {
    id: number;
    contact_service: string;
    contact_type: string | null;
    balance: number;
    currency_code: string;
    first_name: string;
    last_name: string | null;
    company_name: string | null;
    display_name: string;
    email: string | null;
    work_phone: string | null;
    personal_phone: string | null;
    created_at: string;
    updated_at: string;
  };
  bill_date: string;
  formatted_bill_date: string;
  due_date: string;
  formatted_due_date: string;
  total: number;
  total_formatted: string;
  balance: number;
  formatted_balance: string;
  due_amount: number;
  formatted_due_amount: string;
  payment_amount: number;
  formatted_payment_amount: string;
  currency_code: string;
  created_at: string;
  formatted_created_at: string;
  updated_at: string | null;
  is_paid: boolean;
  is_partially_paid: boolean;
  is_fully_paid: boolean;
  is_overdue: boolean;
  overdue_days: number;
  remaining_days: number;
  note: string;
  reference_no: string;
  entries: Array<{
    id: number;
    description: string | null;
    quantity: number;
    rate: number;
    total: number;
    item: {
      id: number;
      name: string;
      type: string;
      code: string | null;
    };
  }>;
}

// Keep the mock data for other transaction types
const MOCK_OTHER_TRANSACTIONS = [
  {
    id: '3',
    type: 'Kas Kecil',
    description: 'Kopi untuk rapat tim',
    amount: -25000,
    date: '2024-06-15',
    time: '11:20',
    bot: 'AI Kas Kecil',
    status: 'pending',
    category: 'pengeluaran'
  },
  {
    id: '4',
    type: 'Transaksi Umum',
    description: 'Transfer bank ke tabungan',
    amount: -1000000,
    date: '2024-06-14',
    time: '16:15',
    bot: 'AI Transaksi Umum',
    status: 'selesai',
    category: 'transfer'
  }
];

// API service for fetching invoices from new endpoint
const fetchInvoices = async (accessToken?: string): Promise<Invoice[]> => {
  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Add authorization header if token is available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch('https://wabot-n8n.libslm.easypanel.host/webhook/transync/invoices/list', {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    // Handle different possible response structures
    if (Array.isArray(data)) {
      return data;
    } else if (data.data && Array.isArray(data.data)) {
      return data.data;
    } else if (data.invoices && Array.isArray(data.invoices)) {
      return data.invoices;
    } else {
      console.warn('Unexpected API response structure:', data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching invoices:', error);
    toast.error('Gagal memuat data invoice');
    throw error;
  }
};

export const TransactionsTab = () => {
  const { session, user } = useAuth();
  const {
    trackAndValidateTransaction,
    getUsageStatus,
    showUpgradePromptIfNeeded
  } = useUsageTracking();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('semua');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [selectedVendorBill, setSelectedVendorBill] = useState<VendorBill | null>(null);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [vendorBillDialogOpen, setVendorBillDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [vendorBills, setVendorBills] = useState<VendorBill[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get usage status for UI display
  const usageStatus = getUsageStatus();

  // Function to fetch invoices with usage tracking
  const fetchInvoicesData = async () => {
    try {
      // Track this as a transaction fetch operation
      const canProceed = await trackAndValidateTransaction('invoice_fetch');
      if (!canProceed) {
        showUpgradePromptIfNeeded();
        return;
      }

      const accessToken = session?.access_token;
      console.log('Using access token for API request:', accessToken ? 'Token available' : 'No token');

      const fetchedInvoices = await fetchInvoices(accessToken);
      setInvoices(fetchedInvoices);
      toast.success(`Berhasil memuat ${fetchedInvoices.length} invoice`);
    } catch (err) {
      console.error('Error fetching invoices:', err);
      toast.error('Gagal memuat invoice');
      setInvoices([]);
    }
  };

  // Function to fetch vendor bills using BigCapital service with usage tracking
  const fetchVendorBillsData = async () => {
    if (!user) {
      console.log('No user available for vendor bills fetch');
      return;
    }

    try {
      // Track this as a transaction fetch operation
      const canProceed = await trackAndValidateTransaction('vendor_bill_fetch');
      if (!canProceed) {
        showUpgradePromptIfNeeded();
        return;
      }

      console.log('Fetching vendor bills with BigCapital service for user:', user.id);

      const fetchedBills = await bigCapitalService.getVendorBills(user.id);
      setVendorBills(fetchedBills);
      toast.success(`Berhasil memuat ${fetchedBills.length} tagihan vendor`);
    } catch (err) {
      console.error('Error fetching vendor bills:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast.error(`Gagal memuat tagihan vendor: ${errorMessage}`);
      setVendorBills([]);
    }
  };

  // Combined function to fetch all data
  const fetchAllData = async () => {
    setLoading(true);
    setError(null);
    try {
      await Promise.all([fetchInvoicesData(), fetchVendorBillsData()]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch data if user is authenticated
    if (session?.access_token && user) {
      fetchAllData();
    }
  }, [session?.access_token, user]);

  // Transform invoices to match our transaction format
  const invoiceTransactions = invoices.map((invoice) => {
    // Map invoice status to our status
    const getTransactionStatus = () => {
      if (invoice.is_fully_paid) return 'selesai';
      if (invoice.is_overdue) return 'gagal';
      if (invoice.is_partially_paid) return 'pending';
      return 'pending';
    };

    return {
      id: `invoice-${invoice.id}`,
      type: 'Pembayaran Pelanggan',
      description: `Invoice ${invoice.invoice_no}${invoice.customer.display_name ? ` dari ${invoice.customer.display_name}` : ''}`,
      amount: invoice.total,
      date: invoice.invoice_date.split('T')[0],
      time: new Date(invoice.created_at).toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      bot: 'AI Pelanggan',
      status: getTransactionStatus(),
      category: 'pemasukan',
      invoice: invoice // Store the full invoice object for details
    };
  });

  // Transform vendor bills to match our transaction format
  const vendorBillTransactions = vendorBills.map((bill) => {
    // Map bill status to our status
    const getTransactionStatus = () => {
      if (bill.is_fully_paid) return 'selesai';
      if (bill.is_overdue) return 'gagal';
      if (bill.is_partially_paid) return 'pending';
      return 'pending';
    };

    return {
      id: `bill-${bill.id}`,
      type: 'Tagihan Vendor',
      description: `${bill.bill_number || `Bill #${bill.id}`}${bill.vendor.display_name ? ` dari ${bill.vendor.display_name}` : ''}`,
      amount: -bill.total, // Negative for expenses
      date: bill.bill_date.split('T')[0],
      time: new Date(bill.created_at).toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      bot: 'AI Vendor',
      status: getTransactionStatus(),
      category: 'pengeluaran',
      bill: bill // Store the full bill object for details
    };
  });

  // Combine all transactions
  const allTransactions = [...invoiceTransactions, ...vendorBillTransactions, ...MOCK_OTHER_TRANSACTIONS];

  const filteredTransactions = allTransactions.filter(transaction =>
    transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transaction.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'selesai': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'gagal': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number) => {
    const formatted = Math.abs(amount).toLocaleString('id-ID', {
      style: 'currency',
      currency: 'IDR'
    });
    return amount >= 0 ? `+${formatted}` : `-${formatted}`;
  };

  // Calculate totals
  const todayIncome = invoiceTransactions
    .filter(t => t.status === 'selesai')
    .reduce((sum, t) => sum + t.amount, 0);
  
  const todayExpense = [...vendorBillTransactions, ...MOCK_OTHER_TRANSACTIONS]
    .filter(t => t.category === 'pengeluaran' && t.status === 'selesai')
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const netTotal = todayIncome - todayExpense;

  const handleTransactionClick = (transaction: any) => {
    if (transaction.type === 'Pembayaran Pelanggan' && transaction.invoice) {
      setSelectedInvoice(transaction.invoice);
      setInvoiceDialogOpen(true);
    } else if (transaction.type === 'Tagihan Vendor' && transaction.bill) {
      setSelectedVendorBill(transaction.bill);
      setVendorBillDialogOpen(true);
    } else {
      toast.info('Detail hanya tersedia untuk transaksi pembayaran pelanggan dan tagihan vendor');
    }
  };

  const handleRefresh = () => {
    fetchAllData();
  };

  return (
    <div className="w-full h-[calc(100vh-60px)] overflow-y-auto">
      <div className="max-w-4xl mx-auto p-2 sm:p-4">
        <div className="mb-4 sm:mb-6">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-xl sm:text-2xl font-bold">Semua Transaksi</h1>
            <div className="flex items-center gap-2">
              {usageStatus.remaining !== null && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <TrendingUp className="w-4 h-4" />
                  <span>{usageStatus.remaining} tersisa</span>
                </div>
              )}
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* Usage Warning */}
          {usageStatus.atLimit && (
            <Card className="mb-4 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <div className="flex-1">
                    <h4 className="font-medium text-red-800">
                      Batas Transaksi Harian Tercapai
                    </h4>
                    <p className="text-sm text-red-700">
                      Anda telah mencapai batas 10 transaksi hari ini. Upgrade ke plan Unlimited untuk transaksi tanpa batas.
                    </p>
                  </div>
                  <Button
                    size="sm"
                    className="bg-red-600 hover:bg-red-700"
                    onClick={() => showUpgradePromptIfNeeded()}
                  >
                    Upgrade Sekarang
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {usageStatus.remaining !== null && usageStatus.remaining <= 2 && usageStatus.remaining > 0 && (
            <Card className="mb-4 border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-600" />
                  <div className="flex-1">
                    <h4 className="font-medium text-yellow-800">
                      Mendekati Batas Harian
                    </h4>
                    <p className="text-sm text-yellow-700">
                      Anda hanya memiliki {usageStatus.remaining} transaksi tersisa hari ini.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Search and Filter */}
          <div className="flex gap-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Cari transaksi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="w-4 h-4" />
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <Card className="mb-4 border-red-200 bg-red-50">
              <CardContent className="p-4">
                <div className="text-red-600 text-sm">
                  <strong>Error:</strong> {error}
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleRefresh} 
                  className="mt-2"
                  disabled={loading}
                >
                  Coba Lagi
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Summary Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Pemasukan Hari Ini</div>
                <div className="text-lg sm:text-2xl font-bold text-green-600">
                  +{formatAmount(todayIncome).substring(1)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {invoiceTransactions.filter(t => t.status === 'selesai').length} invoice terbayar
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Pengeluaran Hari Ini</div>
                <div className="text-lg sm:text-2xl font-bold text-red-600">
                  -{formatAmount(todayExpense).substring(1)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {vendorBillTransactions.filter(t => t.status === 'selesai').length} tagihan vendor terbayar
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-3 sm:p-4">
                <div className="text-xs sm:text-sm text-muted-foreground">Total Bersih</div>
                <div className={`text-lg sm:text-2xl font-bold ${netTotal >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatAmount(netTotal)}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Transactions List */}
        <div className="space-y-2 sm:space-y-3">
          {loading && (
            <Card>
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Memuat data transaksi...</span>
                </div>
              </CardContent>
            </Card>
          )}

          {!loading && filteredTransactions.length === 0 && (
            <Card>
              <CardContent className="p-4 text-center text-muted-foreground">
                {searchTerm ? 'Tidak ada transaksi yang sesuai dengan pencarian' : 'Belum ada transaksi'}
              </CardContent>
            </Card>
          )}
          
          {filteredTransactions.map((transaction) => (
            <Card 
              key={transaction.id} 
              className={`hover:shadow-md transition-shadow ${
                (transaction.type === 'Pembayaran Pelanggan' || transaction.type === 'Tagihan Vendor') 
                  ? 'cursor-pointer hover:bg-muted/50' : ''
              }`}
              onClick={() => handleTransactionClick(transaction)}
            >
              <CardContent className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                      <h3 className="font-semibold text-sm sm:text-base truncate">{transaction.type}</h3>
                      <div className="flex gap-1 sm:gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {transaction.bot}
                        </Badge>
                        <Badge className={`text-xs ${getStatusColor(transaction.status)}`}>
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-xs sm:text-sm text-muted-foreground mb-2 line-clamp-2">
                      {transaction.description}
                    </p>
                    <div className="text-xs text-muted-foreground">
                      {transaction.date} pada {transaction.time}
                    </div>
                  </div>
                  <div className="flex justify-between sm:block sm:text-right">
                    <div className="sm:hidden text-xs text-muted-foreground">Jumlah:</div>
                    <div className={`text-base sm:text-lg font-bold ${
                      transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatAmount(transaction.amount)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Data Stats */}
        {!loading && (invoices.length > 0 || vendorBills.length > 0) && (
          <Card className="mt-4">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Statistik Data:</strong> 
                {invoices.length > 0 && (
                  <span> Invoice: {invoices.length} total | 
                  Terbayar: {invoices.filter(i => i.is_fully_paid).length} | 
                  Pending: {invoices.filter(i => !i.is_fully_paid && !i.is_overdue).length} | 
                  Overdue: {invoices.filter(i => i.is_overdue).length}</span>
                )}
                {vendorBills.length > 0 && (
                  <span> | Tagihan Vendor: {vendorBills.length} total | 
                  Terbayar: {vendorBills.filter(b => b.is_fully_paid).length} | 
                  Pending: {vendorBills.filter(b => !b.is_fully_paid && !b.is_overdue).length} | 
                  Overdue: {vendorBills.filter(b => b.is_overdue).length}</span>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <InvoiceDetailDialog
        invoice={selectedInvoice}
        open={invoiceDialogOpen}
        onOpenChange={setInvoiceDialogOpen}
      />

      <VendorBillDetailDialog
        vendorBill={selectedVendorBill}
        open={vendorBillDialogOpen}
        onOpenChange={setVendorBillDialogOpen}
      />
    </div>
  );
};
