# Sistem Billing untuk Tim

Dokumentasi lengkap untuk sistem billing yang telah diimplementasikan dalam aplikasi Chat Ledger Flow.

## Overview

Sistem billing ini memungkinkan pengelolaan tim dengan skema kuota berdasarkan:
- **Free Plan**: 10 transaksi per hari, gratis
- **Unlimited Plan**: Transaksi unlimited, Rp 500.000 per bulan

## Fitur Utama

### 1. Manajemen Tim
- Pembuatan tim dengan owner
- Undangan anggota tim
- Pengaturan role dan permission
- Manajemen status anggota

### 2. Billing Plans
- Free Plan (10 transaksi/hari)
- Unlimited Plan (unlimited transaksi)
- Upgrade/downgrade otomatis
- Tracking penggunaan real-time

### 3. Usage Tracking
- Pelacakan transaksi harian
- Pembatasan akses berdasarkan kuota
- Warning mendekati limit
- Reset otomatis setiap hari

### 4. UI Components
- Billing dashboard
- Usage statistics
- Plan comparison
- Upgrade prompts

## Struktur Database

### Tables

#### `teams`
```sql
- id: UUID (Primary Key)
- name: TEXT
- owner_id: UUID (Foreign Key to auth.users)
- billing_plan: TEXT ('free' | 'unlimited')
- billing_status: TEXT ('active' | 'suspended' | 'cancelled')
- subscription_start_date: TIMESTAMP
- subscription_end_date: TIMESTAMP
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `team_members`
```sql
- id: UUID (Primary Key)
- team_id: UUID (Foreign Key to teams)
- user_id: UUID (Foreign Key to auth.users)
- role: TEXT ('owner' | 'admin' | 'member')
- permissions: TEXT[]
- status: TEXT ('pending' | 'active' | 'suspended')
- invited_by: UUID (Foreign Key to auth.users)
- invited_at: TIMESTAMP
- joined_at: TIMESTAMP
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `billing_plans`
```sql
- id: UUID (Primary Key)
- name: TEXT (Unique)
- display_name: TEXT
- description: TEXT
- price_monthly: INTEGER (in cents/rupiah)
- daily_transaction_limit: INTEGER (NULL = unlimited)
- features: JSONB
- is_active: BOOLEAN
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `transaction_usage`
```sql
- id: UUID (Primary Key)
- team_id: UUID (Foreign Key to teams)
- user_id: UUID (Foreign Key to auth.users)
- transaction_date: DATE
- transaction_count: INTEGER
- transaction_type: TEXT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### `billing_history`
```sql
- id: UUID (Primary Key)
- team_id: UUID (Foreign Key to teams)
- plan_name: TEXT
- amount: INTEGER
- currency: TEXT
- payment_status: TEXT
- payment_method: TEXT
- payment_reference: TEXT
- billing_period_start: DATE
- billing_period_end: DATE
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### Functions

#### `can_perform_transaction(team_id, user_id)`
Mengecek apakah user dapat melakukan transaksi berdasarkan kuota harian.

#### `increment_transaction_usage(team_id, user_id, transaction_type)`
Menambah counter penggunaan transaksi dan mengembalikan jumlah saat ini.

#### `get_team_usage_summary(team_id)`
Mendapatkan ringkasan penggunaan tim termasuk limit dan sisa kuota.

## API Services

### BillingService

#### Team Management
- `createTeam(request)`: Membuat tim baru
- `getTeam(teamId)`: Mendapatkan detail tim
- `getUserTeams()`: Mendapatkan semua tim user
- `updateTeam(teamId, updates)`: Update data tim

#### Member Management
- `inviteTeamMember(request)`: Undang anggota baru
- `addTeamMember(request)`: Tambah anggota langsung
- `removeTeamMember(teamId, userId)`: Hapus anggota

#### Billing
- `getBillingPlans()`: Mendapatkan semua plan
- `upgradePlan(request)`: Upgrade plan tim
- `trackTransaction(request)`: Track penggunaan transaksi
- `getUsageSummary(teamId)`: Ringkasan penggunaan
- `canPerformTransaction(teamId, userId)`: Cek izin transaksi

## Hooks

### `useBilling()`
Hook utama untuk manajemen billing dengan state dan actions:

```typescript
const {
  // State
  loading,
  teams,
  currentTeam,
  billingPlans,
  
  // Actions
  createTeam,
  inviteTeamMember,
  removeTeamMember,
  upgradePlan,
  trackTransaction,
  
  // Helpers
  getCurrentPlan,
  getUsageSummary,
  isOwner,
  canManageBilling,
  getRemainingTransactions,
  isAtTransactionLimit,
  needsUpgrade
} = useBilling();
```

### `useUsageTracking()`
Hook khusus untuk tracking penggunaan:

```typescript
const {
  trackAndValidateTransaction,
  validateTransactionLimit,
  showUpgradePromptIfNeeded,
  getUsageStatus
} = useUsageTracking();
```

## UI Components

### `BillingPlanCard`
Menampilkan detail plan dengan tombol upgrade.

### `UsageStatistics`
Menampilkan statistik penggunaan dan warning.

### `BillingDialog`
Dialog lengkap untuk manajemen billing dengan tabs:
- Usage: Statistik penggunaan
- Plans: Perbandingan dan upgrade plan
- Settings: Pengaturan billing

## Integrasi dengan Existing Code

### TransactionsTab
- Menambahkan usage tracking pada fetch operations
- Warning display untuk limit
- Upgrade prompts

### TeamTab
- Billing overview cards
- Usage statistics
- Plan management

## Security & Permissions

### Row Level Security (RLS)
Semua tabel menggunakan RLS untuk memastikan:
- User hanya bisa akses tim mereka
- Owner/admin bisa manage tim
- Member hanya bisa view sesuai permission

### Permissions
- `kelola_tim`: Manage team members
- `kelola_billing`: Manage billing settings
- `lihat_laporan`: View reports
- `setujui_kas_kecil`: Approve petty cash
- `kelola_integrasi`: Manage integrations
- `export_data`: Export data

## Testing

### Unit Tests
- Service functions
- Hook behaviors
- Component rendering

### Integration Tests
- Complete billing workflow
- Limit enforcement
- Plan upgrades

### Performance Tests
- Usage tracking speed
- Database query optimization

## Deployment Checklist

1. ✅ Database migration applied
2. ✅ Types updated
3. ✅ Services implemented
4. ✅ Hooks created
5. ✅ UI components built
6. ✅ Integration completed
7. ✅ Tests written
8. ⏳ Payment gateway integration (future)
9. ⏳ Email notifications (future)
10. ⏳ Analytics dashboard (future)

## Usage Examples

### Membuat Tim Baru
```typescript
const success = await createTeam({
  name: "Tim Keuangan",
  billing_plan: "free",
  initial_members: [
    {
      email: "<EMAIL>",
      role: "member",
      permissions: ["lihat_laporan"]
    }
  ]
});
```

### Track Transaksi
```typescript
const canProceed = await trackAndValidateTransaction("invoice_creation");
if (canProceed) {
  // Proceed with transaction
} else {
  // Show upgrade prompt
}
```

### Upgrade Plan
```typescript
const success = await upgradePlan({
  team_id: currentTeam.id,
  new_plan: "unlimited"
});
```

## Troubleshooting

### Common Issues

1. **User tidak bisa create team**
   - Check authentication
   - Verify RLS policies

2. **Usage tracking tidak berfungsi**
   - Check team membership
   - Verify database functions

3. **Upgrade gagal**
   - Check permissions
   - Verify payment integration

### Debug Commands

```sql
-- Check team membership
SELECT * FROM team_members WHERE user_id = 'user-id';

-- Check usage today
SELECT * FROM transaction_usage WHERE team_id = 'team-id' AND transaction_date = CURRENT_DATE;

-- Check plan limits
SELECT * FROM billing_plans WHERE name = 'free';
```

## Future Enhancements

1. **Payment Gateway Integration**
   - Midtrans/Xendit integration
   - Automatic billing
   - Invoice generation

2. **Advanced Analytics**
   - Usage trends
   - Cost optimization
   - Team performance metrics

3. **Enterprise Features**
   - Custom plans
   - Volume discounts
   - Advanced permissions

4. **Notifications**
   - Email alerts
   - WhatsApp notifications
   - Slack integration
