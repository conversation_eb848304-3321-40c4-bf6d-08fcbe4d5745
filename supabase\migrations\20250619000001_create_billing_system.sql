-- Create billing system tables for team management and billing

-- Create teams table
CREATE TABLE public.teams (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  owner_id UUID REFERENCES auth.users NOT NULL,
  billing_plan TEXT NOT NULL DEFAULT 'free' CHECK (billing_plan IN ('free', 'unlimited')),
  billing_status TEXT NOT NULL DEFAULT 'active' CHECK (billing_status IN ('active', 'suspended', 'cancelled')),
  subscription_start_date TIMESTAMP WITH TIME ZONE,
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create team_members table
CREATE TABLE public.team_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  permissions TEXT[] DEFAULT ARRAY[]::TEXT[],
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'suspended')),
  invited_by UUID REFERENCES auth.users,
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  joined_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(team_id, user_id)
);

-- Create billing_plans table for plan definitions
CREATE TABLE public.billing_plans (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  display_name TEXT NOT NULL,
  description TEXT,
  price_monthly INTEGER NOT NULL DEFAULT 0, -- in cents/rupiah
  daily_transaction_limit INTEGER, -- NULL means unlimited
  features JSONB DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create transaction_usage table for tracking daily usage
CREATE TABLE public.transaction_usage (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users NOT NULL,
  transaction_date DATE NOT NULL DEFAULT CURRENT_DATE,
  transaction_count INTEGER NOT NULL DEFAULT 0,
  transaction_type TEXT, -- 'invoice', 'bill', 'expense', etc.
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(team_id, user_id, transaction_date)
);

-- Create billing_history table for payment tracking
CREATE TABLE public.billing_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
  plan_name TEXT NOT NULL,
  amount INTEGER NOT NULL, -- in cents/rupiah
  currency TEXT NOT NULL DEFAULT 'IDR',
  payment_status TEXT NOT NULL DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
  payment_method TEXT,
  payment_reference TEXT,
  billing_period_start DATE NOT NULL,
  billing_period_end DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default billing plans
INSERT INTO public.billing_plans (name, display_name, description, price_monthly, daily_transaction_limit, features) VALUES
('free', 'Free Plan', 'Gratis dengan batasan 10 transaksi per hari', 0, 10, '{"whatsapp_integration": true, "basic_reports": true, "team_members": 3}'),
('unlimited', 'Unlimited Plan', 'Unlimited transaksi dengan fitur lengkap', 50000000, NULL, '{"whatsapp_integration": true, "advanced_reports": true, "team_members": null, "priority_support": true, "custom_integrations": true}');

-- Add Row Level Security (RLS)
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transaction_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billing_history ENABLE ROW LEVEL SECURITY;

-- Create policies for teams table
CREATE POLICY "Users can view teams they own or are members of" 
  ON public.teams 
  FOR SELECT 
  USING (
    owner_id = auth.uid() OR 
    id IN (SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND status = 'active')
  );

CREATE POLICY "Users can create teams" 
  ON public.teams 
  FOR INSERT 
  WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Team owners can update their teams" 
  ON public.teams 
  FOR UPDATE 
  USING (owner_id = auth.uid());

CREATE POLICY "Team owners can delete their teams" 
  ON public.teams 
  FOR DELETE 
  USING (owner_id = auth.uid());

-- Create policies for team_members table
CREATE POLICY "Users can view team members of teams they belong to" 
  ON public.team_members 
  FOR SELECT 
  USING (
    team_id IN (
      SELECT id FROM public.teams WHERE owner_id = auth.uid()
      UNION
      SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Team owners and admins can manage team members" 
  ON public.team_members 
  FOR ALL 
  USING (
    team_id IN (
      SELECT id FROM public.teams WHERE owner_id = auth.uid()
      UNION
      SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND role IN ('owner', 'admin') AND status = 'active'
    )
  );

-- Create policies for billing_plans table (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view billing plans" 
  ON public.billing_plans 
  FOR SELECT 
  TO authenticated
  USING (is_active = true);

-- Create policies for transaction_usage table
CREATE POLICY "Users can view usage for their teams" 
  ON public.transaction_usage 
  FOR SELECT 
  USING (
    team_id IN (
      SELECT id FROM public.teams WHERE owner_id = auth.uid()
      UNION
      SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can insert usage for their teams" 
  ON public.transaction_usage 
  FOR INSERT 
  WITH CHECK (
    team_id IN (
      SELECT id FROM public.teams WHERE owner_id = auth.uid()
      UNION
      SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND status = 'active'
    )
  );

CREATE POLICY "Users can update usage for their teams" 
  ON public.transaction_usage 
  FOR UPDATE 
  USING (
    team_id IN (
      SELECT id FROM public.teams WHERE owner_id = auth.uid()
      UNION
      SELECT team_id FROM public.team_members WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Create policies for billing_history table
CREATE POLICY "Team owners can view billing history" 
  ON public.billing_history 
  FOR SELECT 
  USING (
    team_id IN (SELECT id FROM public.teams WHERE owner_id = auth.uid())
  );

CREATE POLICY "Team owners can insert billing history" 
  ON public.billing_history 
  FOR INSERT 
  WITH CHECK (
    team_id IN (SELECT id FROM public.teams WHERE owner_id = auth.uid())
  );

-- Create indexes for better performance
CREATE INDEX idx_teams_owner_id ON public.teams(owner_id);
CREATE INDEX idx_team_members_team_id ON public.team_members(team_id);
CREATE INDEX idx_team_members_user_id ON public.team_members(user_id);
CREATE INDEX idx_transaction_usage_team_date ON public.transaction_usage(team_id, transaction_date);
CREATE INDEX idx_transaction_usage_user_date ON public.transaction_usage(user_id, transaction_date);
CREATE INDEX idx_billing_history_team_id ON public.billing_history(team_id);

-- Create functions for usage tracking
CREATE OR REPLACE FUNCTION public.increment_transaction_usage(
  p_team_id UUID,
  p_user_id UUID,
  p_transaction_type TEXT DEFAULT NULL
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Insert or update transaction usage for today
  INSERT INTO public.transaction_usage (team_id, user_id, transaction_date, transaction_count, transaction_type)
  VALUES (p_team_id, p_user_id, CURRENT_DATE, 1, p_transaction_type)
  ON CONFLICT (team_id, user_id, transaction_date)
  DO UPDATE SET 
    transaction_count = transaction_usage.transaction_count + 1,
    updated_at = now()
  RETURNING transaction_count INTO current_count;
  
  RETURN current_count;
END;
$$;

-- Create function to check if team can perform transaction
CREATE OR REPLACE FUNCTION public.can_perform_transaction(
  p_team_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  team_plan TEXT;
  daily_limit INTEGER;
  current_usage INTEGER;
BEGIN
  -- Get team's billing plan
  SELECT billing_plan INTO team_plan
  FROM public.teams
  WHERE id = p_team_id;
  
  -- Get plan's daily limit
  SELECT daily_transaction_limit INTO daily_limit
  FROM public.billing_plans
  WHERE name = team_plan;
  
  -- If unlimited plan (daily_limit is NULL), allow transaction
  IF daily_limit IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Get current usage for today
  SELECT COALESCE(transaction_count, 0) INTO current_usage
  FROM public.transaction_usage
  WHERE team_id = p_team_id 
    AND user_id = p_user_id 
    AND transaction_date = CURRENT_DATE;
  
  -- Check if under limit
  RETURN current_usage < daily_limit;
END;
$$;

-- Create function to get team usage summary
CREATE OR REPLACE FUNCTION public.get_team_usage_summary(p_team_id UUID)
RETURNS TABLE (
  total_members INTEGER,
  current_plan TEXT,
  daily_limit INTEGER,
  today_usage INTEGER,
  remaining_today INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM public.team_members WHERE team_id = p_team_id AND status = 'active') as total_members,
    t.billing_plan as current_plan,
    bp.daily_transaction_limit as daily_limit,
    COALESCE(SUM(tu.transaction_count)::INTEGER, 0) as today_usage,
    CASE 
      WHEN bp.daily_transaction_limit IS NULL THEN NULL
      ELSE GREATEST(0, bp.daily_transaction_limit - COALESCE(SUM(tu.transaction_count)::INTEGER, 0))
    END as remaining_today
  FROM public.teams t
  LEFT JOIN public.billing_plans bp ON bp.name = t.billing_plan
  LEFT JOIN public.transaction_usage tu ON tu.team_id = t.id AND tu.transaction_date = CURRENT_DATE
  WHERE t.id = p_team_id
  GROUP BY t.billing_plan, bp.daily_transaction_limit;
END;
$$;
