import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BillingPlanCard } from './BillingPlanCard';
import { UsageStatistics } from './UsageStatistics';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  TrendingUp, 
  Settings,
  AlertCircle
} from 'lucide-react';
import { useBilling } from '@/hooks/useBilling';
import { BillingUpgradeRequest } from '@/types/billing';

interface BillingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const BillingDialog: React.FC<BillingDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const {
    loading,
    currentTeam,
    billingPlans,
    getCurrentPlan,
    getUsageSummary,
    upgradePlan,
    canManageBilling,
    isOwner
  } = useBilling();

  const [upgradeLoading, setUpgradeLoading] = useState(false);

  const currentPlan = getCurrentPlan();
  const usageSummary = getUsageSummary();

  const handleUpgrade = async (planName: string) => {
    if (!currentTeam) return;

    setUpgradeLoading(true);
    try {
      const request: BillingUpgradeRequest = {
        team_id: currentTeam.id,
        new_plan: planName
      };

      const success = await upgradePlan(request);
      if (success) {
        onOpenChange(false);
      }
    } finally {
      setUpgradeLoading(false);
    }
  };

  if (!currentTeam) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Billing & Usage</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Pilih tim terlebih dahulu untuk melihat informasi billing.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const canManage = canManageBilling();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Billing & Usage - {currentTeam.name}
            </DialogTitle>
            <div className="flex items-center gap-2">
              {!canManage && (
                <Badge variant="secondary" className="text-xs">
                  View Only
                </Badge>
              )}
              {isOwner() && (
                <Badge className="bg-blue-100 text-blue-800 text-xs">
                  Owner
                </Badge>
              )}
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="usage" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="usage" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Usage
            </TabsTrigger>
            <TabsTrigger value="plans" className="flex items-center gap-2">
              <CreditCard className="w-4 h-4" />
              Plans
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="usage" className="space-y-6">
            <UsageStatistics
              usageSummary={usageSummary}
              currentPlan={currentPlan}
              onUpgrade={() => {
                // Switch to plans tab
                const plansTab = document.querySelector('[value="plans"]') as HTMLElement;
                plansTab?.click();
              }}
              loading={loading}
            />
          </TabsContent>

          <TabsContent value="plans" className="space-y-6">
            <div className="space-y-4">
              <div className="text-center space-y-2">
                <h3 className="text-2xl font-bold">Pilih Plan yang Tepat</h3>
                <p className="text-muted-foreground">
                  Upgrade atau downgrade plan sesuai kebutuhan tim Anda
                </p>
              </div>

              {!canManage && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                    <div>
                      <h4 className="font-medium text-yellow-800">
                        Akses Terbatas
                      </h4>
                      <p className="text-sm text-yellow-700">
                        Hanya owner dan admin dengan permission billing yang dapat mengubah plan.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {billingPlans.map((plan) => (
                  <BillingPlanCard
                    key={plan.id}
                    plan={plan}
                    currentPlan={currentPlan?.name}
                    onUpgrade={canManage ? handleUpgrade : undefined}
                    loading={upgradeLoading}
                  />
                ))}
              </div>

              {billingPlans.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Tidak ada plan yang tersedia saat ini.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold">Pengaturan Billing</h3>
              
              {!canManage ? (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                  <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-700 mb-2">
                    Akses Terbatas
                  </h4>
                  <p className="text-sm text-gray-600">
                    Anda tidak memiliki permission untuk mengubah pengaturan billing.
                    Hubungi owner atau admin tim untuk melakukan perubahan.
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Team Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium mb-3">Informasi Tim</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Nama Tim:</span>
                        <p className="font-medium">{currentTeam.name}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Plan Saat Ini:</span>
                        <p className="font-medium">{currentPlan?.display_name}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Status Billing:</span>
                        <Badge 
                          className={
                            currentTeam.billing_status === 'active' 
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }
                        >
                          {currentTeam.billing_status}
                        </Badge>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Anggota Tim:</span>
                        <p className="font-medium">{usageSummary?.total_members || 0}</p>
                      </div>
                    </div>
                  </div>

                  {/* Billing Actions */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Aksi Billing</h4>
                    
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full justify-start">
                        <CreditCard className="w-4 h-4 mr-2" />
                        Lihat Riwayat Pembayaran
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start">
                        <TrendingUp className="w-4 h-4 mr-2" />
                        Download Laporan Usage
                      </Button>
                    </div>
                  </div>

                  {/* Danger Zone */}
                  {isOwner() && (
                    <div className="border border-red-200 rounded-lg p-4">
                      <h4 className="font-medium text-red-800 mb-2">Danger Zone</h4>
                      <p className="text-sm text-red-600 mb-4">
                        Aksi ini tidak dapat dibatalkan dan akan mempengaruhi seluruh tim.
                      </p>
                      <Button variant="destructive" size="sm">
                        Cancel Subscription
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
